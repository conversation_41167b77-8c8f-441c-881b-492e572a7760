(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[879],{4747:(e,s,a)=>{Promise.resolve().then(a.bind(a,9349))},5695:(e,s,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(s,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(s,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(s,{useRouter:function(){return r.useRouter}})},9349:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});var r=a(5155),n=a(2115),l=a(5695);function t(){let[e,s]=(0,n.useState)({first_name:"",last_name:"",username:"",email:"",password:"",confirmPassword:""}),[a,t]=(0,n.useState)(""),[i,o]=(0,n.useState)(!1),d=(0,l.useRouter)(),m=a=>{s({...e,[a.target.name]:a.target.value})},c=async s=>{if(s.preventDefault(),t(""),e.password!==e.confirmPassword)return void t("Passwords do not match");o(!0);try{let s=await fetch("http://172.24.175.70:5001/api/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({first_name:e.first_name,last_name:e.last_name,username:e.username,email:e.email,password:e.password})}),a=await s.json();if(!s.ok)throw Error(a.error||"Registration failed");d.push("/signin")}catch(e){t(e.message)}finally{o(!1)}};return(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen bg-[#fafbfc]",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center mb-8",children:[(0,r.jsx)("div",{className:"bg-orange-500 rounded-lg p-3 mb-4",children:(0,r.jsx)("svg",{width:"32",height:"32",fill:"white",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z"})})}),(0,r.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Create your account"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Join the AI Trainer platform"})]}),(0,r.jsxs)("form",{onSubmit:c,className:"bg-white p-8 rounded-lg shadow-md w-full max-w-md",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Sign Up"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Create your account to start learning or teaching"}),(0,r.jsxs)("div",{className:"mb-4 flex gap-2",children:[(0,r.jsxs)("div",{className:"w-1/2",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"First Name"}),(0,r.jsx)("input",{type:"text",name:"first_name",className:"w-full border rounded px-3 py-2",value:e.first_name,onChange:m,required:!0,placeholder:"John"})]}),(0,r.jsxs)("div",{className:"w-1/2",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Last Name"}),(0,r.jsx)("input",{type:"text",name:"last_name",className:"w-full border rounded px-3 py-2",value:e.last_name,onChange:m,required:!0,placeholder:"Doe"})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Username"}),(0,r.jsx)("input",{type:"text",name:"username",className:"w-full border rounded px-3 py-2",value:e.username,onChange:m,required:!0,placeholder:"johndoe"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Email"}),(0,r.jsx)("input",{type:"email",name:"email",className:"w-full border rounded px-3 py-2",value:e.email,onChange:m,required:!0,placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"I want to"}),(0,r.jsx)("select",{className:"w-full border rounded px-3 py-2 bg-white",disabled:!0,children:(0,r.jsx)("option",{children:"Learn (take courses)"})})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Password"}),(0,r.jsx)("input",{type:"password",name:"password",className:"w-full border rounded px-3 py-2",value:e.password,onChange:m,required:!0,placeholder:"Enter your password"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("label",{className:"block mb-1 font-medium",children:"Confirm Password"}),(0,r.jsx)("input",{type:"password",name:"confirmPassword",className:"w-full border rounded px-3 py-2",value:e.confirmPassword,onChange:m,required:!0,placeholder:"Confirm your password"})]}),a&&(0,r.jsx)("div",{className:"text-red-500 mb-2",children:a}),(0,r.jsx)("button",{type:"submit",className:"w-full bg-orange-500 text-white py-2 rounded mt-2 hover:bg-orange-600 transition",disabled:i,children:i?"Creating Account...":"Create Account"}),(0,r.jsxs)("div",{className:"text-center mt-4 text-gray-600",children:["Already have an account? ",(0,r.jsx)("a",{href:"/signin",className:"text-orange-500 hover:underline",children:"Sign in"})]})]})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[441,684,358],()=>s(4747)),_N_E=e.O()}]);