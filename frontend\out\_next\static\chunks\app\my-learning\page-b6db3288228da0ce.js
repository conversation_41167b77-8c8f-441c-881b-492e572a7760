(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[794],{5532:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>o});var r=t(5155),a=t(5494),l=t(2115),n=t(7489),c=t(3578);let i=e=>{let{course:s}=e;return(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-4 flex items-center space-x-4 ".concat("completed"===s.status?"border border-green-200":""),children:[(0,r.jsx)("div",{className:"w-32 h-20 rounded-md bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center flex-shrink-0",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-1",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.84L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.84l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"})})}),(0,r.jsx)("p",{className:"text-blue-700 text-xs font-medium",children:s.domain})]})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-800",children:s.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-gray-600 text-xs mt-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)(n.g,{icon:c.a$,className:"mr-0.5"}),s.estimated_hours," hours"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)(n.g,{icon:c.ReK,className:"mr-0.5"}),s.completed_modules,"/",s.total_modules," modules"]}),(0,r.jsxs)("p",{className:"text-gray-500",children:[s.domain," • ",s.level]})]}),"in_progress"===s.status&&(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1.5",children:(0,r.jsx)("div",{className:"h-1.5 rounded-full",style:{width:"".concat(s.progress_percentage,"%"),background:"linear-gradient(to right, #fb923c, #22c55e)"}})}),(0,r.jsxs)("p",{className:"text-right text-xs text-gray-600 mt-1",children:[s.progress_percentage,"%"]})]}),"completed"===s.status&&(0,r.jsx)("div",{className:"mt-2 text-gray-600 text-xs",children:(0,r.jsxs)("p",{children:["Completed on ",(e=>e?new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"numeric",day:"numeric"}):"N/A")(s.completed_at)]})})]}),(0,r.jsxs)("div",{className:"flex flex-col items-end space-y-1.5",children:["in_progress"===s.status&&(0,r.jsx)("span",{className:"inline-block bg-orange-100 text-orange-700 text-xs px-2 py-0.5 rounded-full font-medium",children:s.level}),"completed"===s.status&&(0,r.jsx)("span",{className:"inline-block bg-green-100 text-green-700 text-xs px-2 py-0.5 rounded-full font-medium",children:"Completed"}),"in_progress"===s.status&&(0,r.jsx)("a",{href:"/courses/".concat(s.course_id),className:"bg-orange-500 text-white px-3 py-1 rounded-md hover:bg-orange-600 transition-colors duration-200 text-sm",children:"Continue Learning"}),"completed"===s.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("a",{href:"/courses/".concat(s.course_id),className:"bg-gray-200 text-gray-800 px-3 py-1 rounded-md hover:bg-gray-300 transition-colors duration-200 text-sm",children:"Review"}),(0,r.jsx)("a",{href:"/my-certificates",className:"bg-orange-500 text-white px-3 py-1 rounded-md hover:bg-orange-600 transition-colors duration-200 text-sm",children:"View Certificate"})]})]})]})};function o(){let[e,s]=(0,l.useState)(null),[t,o]=(0,l.useState)(!0),[d,x]=(0,l.useState)(null),[m,u]=(0,l.useState)(null),h=()=>{let e=localStorage.getItem("token");if(e)try{let s=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),t=decodeURIComponent(atob(s).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(t)}catch(e){console.error("Error decoding token:",e)}return null};(0,l.useEffect)(()=>{let e=h();e?u(e):window.location.href="/signin"},[]);let g=null==m?void 0:m.id;if((0,l.useEffect)(()=>{let e=async()=>{if(g)try{o(!0);let e=await fetch("http://172.24.175.70:5001"+"/api/users/".concat(g,"/dashboard"));if(!e.ok)throw Error("Failed to fetch dashboard data");let t=await e.json();s(t)}catch(e){x(e instanceof Error?e.message:"An error occurred")}finally{o(!1)}};m&&g&&e()},[g,m]),t)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-gray-600",children:"Loading your learning progress..."})})})]});if(d||!e)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-red-600",children:d||"Failed to load learning data"})})})]});let{courses:f}=e,p=f.filter(e=>"in_progress"===e.status),j=f.filter(e=>"completed"===e.status);return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("main",{className:"flex-grow container mx-auto px-4 py-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-2",children:"My Learning"}),(0,r.jsx)("p",{className:"text-gray-600 text-base mb-4",children:"Continue where you left off and track your progress"}),(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:["In Progress (",p.length,")"]}),p.length>0?(0,r.jsx)("div",{className:"space-y-6 mb-8",children:p.map(e=>(0,r.jsx)(i,{course:e},e.course_id))}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center mb-8",children:[(0,r.jsx)(n.g,{icon:c.ReK,className:"text-gray-400 text-3xl mb-3"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No courses in progress"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Start a new course to begin your learning journey"}),(0,r.jsx)("a",{href:"/courses",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:"Browse Courses"})]}),(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800 mb-4",children:["Completed (",j.length,")"]}),j.length>0?(0,r.jsx)("div",{className:"space-y-6",children:j.map(e=>(0,r.jsx)(i,{course:e},e.course_id))}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 text-center",children:[(0,r.jsx)(n.g,{icon:c.SGM,className:"text-gray-400 text-3xl mb-3"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No completed courses yet"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Complete your first course to earn a certificate"})]})]})]})}},8780:(e,s,t)=>{Promise.resolve().then(t.bind(t,5532))}},e=>{var s=s=>e(e.s=s);e.O(0,[266,298,581,494,441,684,358],()=>s(8780)),_N_E=e.O()}]);