(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[432],{2098:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>n});var r=t(5155),a=t(5494),l=t(2115),c=t(7489),d=t(3578);function n(){let[e,s]=(0,l.useState)(null),[t,n]=(0,l.useState)(!0),[i,o]=(0,l.useState)(null),[x,m]=(0,l.useState)(null),g=()=>{let e=localStorage.getItem("token");if(e)try{let s=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),t=decodeURIComponent(atob(s).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(t)}catch(e){console.error("Error decoding token:",e)}return null};(0,l.useEffect)(()=>{let e=g();e?m(e):window.location.href="/signin"},[]);let h=null==x?void 0:x.id;if((0,l.useEffect)(()=>{let e=async()=>{if(h)try{n(!0);let e=await fetch("http://172.24.175.70:5001"+"/api/users/".concat(h,"/dashboard"));if(!e.ok)throw Error("Failed to fetch dashboard data");let t=await e.json();s(t)}catch(e){o(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}};x&&h&&e()},[h,x]),t)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-gray-600",children:"Loading dashboard..."})})})]});if(i||!e)return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,r.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,r.jsx)("div",{className:"text-lg text-red-600",children:i||"Failed to load dashboard"})})})]});let{statistics:u,courses:p}=e;return(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,r.jsx)(a.default,{}),(0,r.jsxs)("main",{className:"flex-grow container mx-auto px-4 py-8",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"My Learning Dashboard"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Track your progress and continue your learning journey"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-orange-100 p-3 rounded-lg",children:(0,r.jsx)(c.g,{icon:d.ReK,className:"text-orange-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:u.in_progress_courses}),(0,r.jsx)("p",{className:"text-gray-600",children:"Courses in Progress"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-green-100 p-3 rounded-lg",children:(0,r.jsx)(c.g,{icon:d.SGM,className:"text-green-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:u.completed_courses}),(0,r.jsx)("p",{className:"text-gray-600",children:"Completed Courses"})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-6 border border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"bg-purple-100 p-3 rounded-lg",children:(0,r.jsx)(c.g,{icon:d.fmL,className:"text-purple-600 text-xl"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-900",children:u.certificates_earned}),(0,r.jsx)("p",{className:"text-gray-600",children:"Certificates Earned"})]})]})})]}),p.length>0?(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-gray-200",children:(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Your Courses"})}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Course"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Progress"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Last Accessed"}),(0,r.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Action"})]})}),(0,r.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:p.map(e=>(0,r.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.title}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:[e.domain," • ",e.level]}),(0,r.jsxs)("div",{className:"text-xs text-gray-400",children:[e.estimated_hours," hours"]})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[e.progress_percentage,"%"]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[e.completed_modules," of ",e.total_modules," modules"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat("completed"===e.status?"bg-green-500":"bg-orange-500"),style:{width:"".concat(e.progress_percentage,"%")}})})]})}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat("completed"===e.status?"bg-green-100 text-green-800":"in_progress"===e.status?"bg-orange-100 text-orange-800":"bg-gray-100 text-gray-800"),children:"completed"===e.status?"Completed":"in_progress"===e.status?"In Progress":"Not Started"})}),(0,r.jsx)("td",{className:"px-6 py-4 text-sm text-gray-500",children:e.last_accessed_at?new Date(e.last_accessed_at).toLocaleDateString():"Never"}),(0,r.jsx)("td",{className:"px-6 py-4",children:(0,r.jsxs)("a",{href:"/courses/".concat(e.course_id),className:"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:[(0,r.jsx)(c.g,{icon:d.ijD,className:"mr-1"}),"completed"===e.status?"Review":"Continue"]})})]},e.course_id))})]})})]}):(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center",children:[(0,r.jsx)(c.g,{icon:d.ReK,className:"text-gray-400 text-4xl mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No courses started yet"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Start learning by exploring our course catalog"}),(0,r.jsx)("a",{href:"/courses",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 transition-colors",children:"Browse Courses"})]})]})]})}},6136:(e,s,t)=>{Promise.resolve().then(t.bind(t,2098))}},e=>{var s=s=>e(e.s=s);e.O(0,[266,298,581,494,441,684,358],()=>s(6136)),_N_E=e.O()}]);