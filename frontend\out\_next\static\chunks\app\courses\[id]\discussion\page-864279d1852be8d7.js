(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[152],{3422:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>d});var r=t(5155),a=t(2115),l=t(5695),n=t(5494),i=t(7489),o=t(3578);function c(e){let{courseId:s,userId:t}=e,[l,n]=(0,a.useState)([]),[c,d]=(0,a.useState)(null),[x,m]=(0,a.useState)([]),[u,h]=(0,a.useState)(!0),[g,f]=(0,a.useState)(!1),[p,b]=(0,a.useState)(!1),[j,y]=(0,a.useState)(""),[N,v]=(0,a.useState)(!1),[w,k]=(0,a.useState)(""),[C,S]=(0,a.useState)(""),[_,E]=(0,a.useState)(""),P=(0,a.useCallback)(async()=>{try{let e=await fetch("".concat("http://172.24.175.70:5001","/api/courses/").concat(s,"/discussions"));if(e.ok){let s=await e.json();n(s)}}catch(e){console.error("Error fetching discussions:",e)}finally{h(!1)}},[s]),D=(0,a.useCallback)(async e=>{try{let s=await fetch("".concat("http://172.24.175.70:5001","/api/discussions/").concat(e,"/replies"));if(s.ok){let e=await s.json();m(e)}}catch(e){console.error("Error fetching replies:",e)}},[]),L=async e=>{if(e.preventDefault(),w.trim()&&C.trim()&&t){v(!0);try{let e=await fetch("".concat("http://172.24.175.70:5001","/api/courses/").concat(s,"/discussions"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,title:w,content:C})});if(e.ok){let s=await e.json();n([s,...l]),k(""),S(""),f(!1)}}catch(e){console.error("Error creating discussion:",e)}finally{v(!1)}}},q=async e=>{if(e.preventDefault(),_.trim()&&c&&t){v(!0);try{let e=await fetch("".concat("http://172.24.175.70:5001","/api/discussions/").concat(c.id,"/replies"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,content:_})});if(e.ok){let s=await e.json();m([...x,s]),E(""),b(!1),n(l.map(e=>e.id===c.id?{...e,reply_count:e.reply_count+1}:e))}}catch(e){console.error("Error creating reply:",e)}finally{v(!1)}}},R=e=>{let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/6e4);return t<1?"Just now":t<60?"".concat(t,"m ago"):t<1440?"".concat(Math.floor(t/60),"h ago"):t<10080?"".concat(Math.floor(t/1440),"d ago"):s.toLocaleDateString()},z=(e,s)=>"".concat(e.charAt(0)).concat(s.charAt(0)).toUpperCase(),M=l.filter(e=>e.title.toLowerCase().includes(j.toLowerCase())||e.content.toLowerCase().includes(j.toLowerCase())||"".concat(e.first_name," ").concat(e.last_name).toLowerCase().includes(j.toLowerCase())),F=e=>{d(e),b(!1),D(e.id)};return((0,a.useEffect)(()=>{s&&t&&P()},[s,t,P]),t)?u?(0,r.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"}),(0,r.jsx)("div",{className:"text-gray-600 font-medium",children:"Loading discussions..."})]})}):(0,r.jsxs)("div",{className:"flex flex-col bg-white rounded-lg shadow-sm",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-gray-200 rounded-lg p-6 mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl",children:(0,r.jsx)(i.g,{icon:o.q9p,className:"text-white text-xl"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"Discussion Forum"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Connect with other learners, ask questions, and share insights"})]})]}),(0,r.jsxs)("button",{onClick:()=>f(!0),className:"bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-6 py-3 rounded-xl flex items-center gap-2 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,r.jsx)(i.g,{icon:o.QLR}),"Ask a Question"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-6 pt-4 border-t border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,r.jsx)(i.g,{icon:o.q9p,className:"text-blue-500"}),(0,r.jsx)("span",{className:"font-medium",children:l.length}),(0,r.jsx)("span",{children:"Discussions"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,r.jsx)(i.g,{icon:o.gdJ,className:"text-green-500"}),(0,r.jsx)("span",{className:"font-medium",children:new Set(l.map(e=>e.username)).size}),(0,r.jsx)("span",{children:"Active Members"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[(0,r.jsx)(i.g,{icon:o.kNw,className:"text-orange-500"}),(0,r.jsx)("span",{className:"font-medium",children:l.reduce((e,s)=>e+s.reply_count,0)}),(0,r.jsx)("span",{children:"Total Replies"})]})]})]}),g&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 p-2 rounded-lg",children:(0,r.jsx)(i.g,{icon:o.DN2,className:"text-white"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-800",children:"Ask a Question"})]}),(0,r.jsx)("button",{onClick:()=>f(!1),className:"text-gray-400 hover:text-gray-600 text-xl",children:(0,r.jsx)(i.g,{icon:o.GRI})})]}),(0,r.jsxs)("form",{onSubmit:L,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-3",children:"Question Title *"}),(0,r.jsx)("input",{type:"text",value:w,onChange:e=>k(e.target.value),placeholder:"What's your question about?",className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-semibold text-gray-700 mb-3",children:"Question Details *"}),(0,r.jsx)("textarea",{value:C,onChange:e=>S(e.target.value),placeholder:"Provide more context about your question. The more details you share, the better answers you'll get!",rows:6,className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 resize-none",required:!0})]}),(0,r.jsxs)("div",{className:"flex gap-3 pt-4",children:[(0,r.jsx)("button",{type:"submit",disabled:N,className:"flex-1 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-6 py-3 rounded-xl flex items-center justify-center gap-2 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none",children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Posting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.g,{icon:o.isI}),"Post Question"]})}),(0,r.jsx)("button",{type:"button",onClick:()=>f(!1),className:"px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-semibold transition-all duration-200",children:"Cancel"})]})]})]})}),(0,r.jsxs)("div",{className:"flex-1 flex gap-6",children:[(0,r.jsxs)("div",{className:"w-1/2 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 px-6 py-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-bold text-white text-lg",children:"All Discussions"}),(0,r.jsxs)("div",{className:"text-white text-sm",children:[M.length," of ",l.length]})]}),(0,r.jsxs)("div",{className:"mt-4 relative",children:[(0,r.jsx)(i.g,{icon:o.MjD,className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-200"}),(0,r.jsx)("input",{type:"text",value:j,onChange:e=>y(e.target.value),placeholder:"Search discussions...",className:"w-full pl-10 pr-4 py-2 bg-white bg-opacity-20 border border-blue-300 rounded-lg text-white placeholder-blue-200 focus:outline-none focus:bg-opacity-30 focus:border-blue-100"})]})]}),(0,r.jsx)("div",{className:"overflow-y-auto max-h-[600px]",children:0===M.length?(0,r.jsx)("div",{className:"p-8 text-center text-gray-500",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.g,{icon:o.MjD,className:"text-4xl mb-3 text-gray-300"}),(0,r.jsxs)("p",{children:['No discussions found matching "',j,'"']})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.g,{icon:o.q9p,className:"text-4xl mb-3 text-gray-300"}),(0,r.jsx)("p",{children:"No discussions yet. Be the first to ask a question!"})]})}):M.map(e=>(0,r.jsx)("div",{onClick:()=>F(e),className:"p-5 border-b border-gray-100 cursor-pointer transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 ".concat((null==c?void 0:c.id)===e.id?"bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-blue-500 shadow-inner":""),children:(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm flex-shrink-0",children:z(e.first_name,e.last_name)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-800 mb-2 line-clamp-2",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-3 line-clamp-2",children:e.content}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-xs text-gray-500",children:[(0,r.jsxs)("span",{className:"font-medium",children:[e.first_name," ",e.last_name]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{children:R(e.created_at)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full",children:[(0,r.jsx)(i.g,{icon:o.Eze}),(0,r.jsx)("span",{children:e.reply_count})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full",children:[(0,r.jsx)(i.g,{icon:o.pS3}),(0,r.jsx)("span",{children:Math.floor(50*Math.random())+10})]})]})]})]})]})},e.id))})]}),(0,r.jsx)("div",{className:"w-1/2 bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200",children:c?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"font-bold text-white text-lg",children:"Discussion Details"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-indigo-200 text-sm",children:[(0,r.jsx)(i.g,{icon:o.Eze}),(0,r.jsxs)("span",{children:[x.length," replies"]})]})]})}),(0,r.jsxs)("div",{className:"flex flex-col h-[600px]",children:[(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-6",children:[(0,r.jsxs)("div",{className:"mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border-l-4 border-blue-500",children:[(0,r.jsxs)("div",{className:"flex items-start gap-4 mb-4",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-indigo-600 text-white w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg flex-shrink-0",children:z(c.first_name,c.last_name)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"font-bold text-gray-800 text-lg mb-2",children:c.title}),(0,r.jsxs)("div",{className:"flex items-center gap-3 text-sm text-gray-600 mb-3",children:[(0,r.jsxs)("span",{className:"font-semibold",children:[c.first_name," ",c.last_name]}),(0,r.jsx)("span",{className:"bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs",children:"Original Poster"}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(i.g,{icon:o.BEE}),(0,r.jsx)("span",{children:R(c.created_at)})]})]})]})]}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:c.content})]}),x.length>0&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h5",{className:"font-semibold text-gray-800 flex items-center gap-2",children:[(0,r.jsx)(i.g,{icon:o.q9p,className:"text-indigo-500"}),"Replies (",x.length,")"]}),x.map((e,s)=>(0,r.jsx)("div",{className:"p-4 bg-gray-50 rounded-xl border border-gray-200 hover:shadow-md transition-shadow duration-200",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-gray-500 to-gray-600 text-white w-10 h-10 rounded-full flex items-center justify-center font-semibold text-sm flex-shrink-0",children:z(e.first_name,e.last_name)}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsxs)("span",{className:"font-semibold text-gray-800",children:[e.first_name," ",e.last_name]}),(0,r.jsx)("span",{className:"text-gray-400",children:"•"}),(0,r.jsx)("span",{className:"text-sm text-gray-500",children:R(e.created_at)}),(0,r.jsxs)("span",{className:"bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs",children:["#",s+1]})]}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:e.content})]})]})},e.id))]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100 p-6",children:p?(0,r.jsxs)("form",{onSubmit:q,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-3",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white w-8 h-8 rounded-full flex items-center justify-center font-semibold text-sm",children:(0,r.jsx)(i.g,{icon:o.Eze})}),(0,r.jsx)("span",{className:"font-semibold text-gray-700",children:"Add your reply"})]}),(0,r.jsx)("textarea",{value:_,onChange:e=>E(e.target.value),placeholder:"Share your thoughts, provide an answer, or ask for clarification...",rows:4,className:"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-200 resize-none",required:!0}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{type:"submit",disabled:N,className:"flex-1 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-4 py-3 rounded-xl flex items-center justify-center gap-2 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:transform-none",children:N?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),"Posting..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.g,{icon:o.isI}),"Post Reply"]})}),(0,r.jsx)("button",{type:"button",onClick:()=>b(!1),className:"px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 font-semibold transition-all duration-200",children:"Cancel"})]})]}):(0,r.jsxs)("button",{onClick:()=>b(!0),className:"w-full bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white py-4 rounded-xl flex items-center justify-center gap-3 font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",children:[(0,r.jsx)(i.g,{icon:o.Eze,className:"text-lg"}),"Join the Discussion"]})})]})]}):(0,r.jsx)("div",{className:"flex items-center justify-center h-[600px] text-gray-500",children:(0,r.jsxs)("div",{className:"text-center p-8",children:[(0,r.jsx)("div",{className:"bg-gradient-to-r from-gray-200 to-gray-300 w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(i.g,{icon:o.q9p,className:"text-3xl text-gray-400"})}),(0,r.jsx)("h4",{className:"text-lg font-semibold text-gray-600 mb-2",children:"Select a Discussion"}),(0,r.jsx)("p",{className:"text-gray-500",children:"Choose a discussion from the left to view details and join the conversation"})]})})})]})]}):(0,r.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"flex flex-col items-center",children:(0,r.jsx)("div",{className:"text-gray-600 font-medium",children:"Please log in to access discussions"})})})}function d(){let e=(0,l.useParams)(),s=(0,l.useRouter)(),t=e.id,[i,o]=(0,a.useState)(null),[d,x]=(0,a.useState)(null),[m,u]=(0,a.useState)(!0),h=()=>{let e=localStorage.getItem("token");if(e)try{let s=e.split(".")[1].replace(/-/g,"+").replace(/_/g,"/"),t=decodeURIComponent(atob(s).split("").map(function(e){return"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)}).join(""));return JSON.parse(t)}catch(e){return console.error("Error decoding token:",e),null}let s=localStorage.getItem("user");if(s)try{return JSON.parse(s)}catch(e){console.error("Error parsing user data:",e)}return null},g=null==d?void 0:d.id;(0,a.useEffect)(()=>{let e=async()=>{try{let e=await fetch("".concat("http://172.24.175.70:5001","/api/courses/").concat(t));if(e.ok){let s=await e.json();o(s)}}catch(e){console.error("Error fetching course:",e)}};t&&e()},[t]),(0,a.useEffect)(()=>{let e=h();e&&x(e),u(!1)},[]);let f=()=>{s.push("/courses/".concat(t))};return m?(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("main",{className:"flex-grow flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading discussion forum..."})]})})]}):(0,r.jsxs)("div",{className:"flex flex-col min-h-screen bg-gray-50",children:[(0,r.jsx)(n.default,{}),(0,r.jsx)("div",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between py-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:f,className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200",children:[(0,r.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"Back to Course"]}),i&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-lg font-semibold text-gray-900",children:i.title}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Discussion Forum"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:null==i?void 0:i.domain}),(0,r.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:null==i?void 0:i.level})]})]})})}),(0,r.jsx)("main",{className:"flex-grow",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:g?(0,r.jsx)(c,{courseId:t,userId:g}):(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-8 text-center",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,r.jsx)("svg",{className:"w-16 h-16 text-gray-400 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Login Required"}),(0,r.jsx)("p",{className:"text-gray-500 mb-6",children:"Please log in to access the discussion forum and connect with other learners."}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)("button",{onClick:()=>s.push("/signin"),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Go to Login"}),(0,r.jsx)("button",{onClick:f,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Back to Course"})]})]})})})})]})}},4864:(e,s,t)=>{Promise.resolve().then(t.bind(t,3422))},5695:(e,s,t)=>{"use strict";var r=t(8999);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}})}},e=>{var s=s=>e(e.s=s);e.O(0,[266,298,581,494,441,684,358],()=>s(4864)),_N_E=e.O()}]);