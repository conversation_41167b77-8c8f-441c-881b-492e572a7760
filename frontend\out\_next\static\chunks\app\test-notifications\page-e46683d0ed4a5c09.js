(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[426],{8434:(e,t,s)=>{Promise.resolve().then(s.bind(s,9692))},9692:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(5155),i=s(2115),r=s(5494);let l=()=>{let[e,t]=(0,i.useState)(""),[s,l]=(0,i.useState)(!1),[o,n]=(0,i.useState)(""),c=async()=>{if(!e)return void n("Please enter a course ID");l(!0),n("");try{let t=localStorage.getItem("token");if(!t){n("Please login first"),l(!1);return}let s=await fetch("http://172.24.175.70:5001/api/test/publish-video",{method:"POST",headers:{Authorization:"Bearer ".concat(t),"Content-Type":"application/json"},body:JSON.stringify({courseId:parseInt(e)})}),a=await s.json();s.ok?n("Success: ".concat(a.message)):n("Error: ".concat(a.error))}catch(e){n("Error: ".concat(e))}finally{l(!1)}},d=async()=>{l(!0),n("");try{let e=localStorage.getItem("token");if(!e){n("Please login first"),l(!1);return}let t=await fetch("http://172.24.175.70:5001/api/notifications/check",{method:"POST",headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}}),s=await t.json();t.ok?n("Success: ".concat(s.message)):n("Error: ".concat(s.error))}catch(e){n("Error: ".concat(e))}finally{l(!1)}};return(0,a.jsxs)("div",{className:"flex flex-col min-h-screen bg-[#fafbfc]",children:[(0,a.jsx)(r.default,{}),(0,a.jsx)("main",{className:"flex-grow container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-8",children:"Test Notification System"}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Simulate Video Publishing"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:'This will update a video\'s status to "published" and trigger a notification.'}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{htmlFor:"courseId",className:"block text-sm font-medium text-gray-700 mb-2",children:"Course ID:"}),(0,a.jsx)("input",{type:"number",id:"courseId",value:e,onChange:e=>t(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500",placeholder:"Enter course ID"})]}),(0,a.jsx)("button",{onClick:c,disabled:s,className:"bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Publishing...":"Publish Video"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mb-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Manual Notification Check"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Manually trigger the notification system to check for new published videos."}),(0,a.jsx)("button",{onClick:d,disabled:s,className:"bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed",children:s?"Checking...":"Trigger Check"})]}),o&&(0,a.jsx)("div",{className:"p-4 rounded-md ".concat(o.startsWith("Success")?"bg-green-100 text-green-800 border border-green-200":"bg-red-100 text-red-800 border border-red-200"),children:o}),(0,a.jsxs)("div",{className:"bg-gray-100 rounded-lg p-6 mt-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"Instructions:"}),(0,a.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-gray-700",children:[(0,a.jsx)("li",{children:"Make sure you're logged in"}),(0,a.jsx)("li",{children:'Enter a course ID that has videos in "draft" status'}),(0,a.jsx)("li",{children:'Click "Publish Video" to simulate publishing'}),(0,a.jsx)("li",{children:"Watch the notification bell in the navbar for real-time updates"}),(0,a.jsx)("li",{children:"You can also manually trigger a check to test the polling system"})]})]})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[266,298,581,494,441,684,358],()=>t(8434)),_N_E=e.O()}]);