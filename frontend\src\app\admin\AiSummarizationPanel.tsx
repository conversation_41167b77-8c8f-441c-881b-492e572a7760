"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { Check, CheckCheck } from "lucide-react";
import { useState, useEffect } from "react";

function PageList({ pages, selected, onSelect }: any) {
  return (
    <div className="w-full mb-8">
      <h3 className=" font-semibold text-brand-blue/[85%] mt-2 mb-4">Medical Course Pages ({pages.length} pages)</h3>
      <ul className="h-full space-y-3 overflow-y-auto max-h-[calc(100vh-5%)] pr-2 custom-scrollbar">
        {pages.map((p: any, idx: number) => (
          <li key={p.page}>
            <button
              className={`w-full text-left px-5 py-3 rounded-xl border transition-all duration-200 flex flex-col gap-1 shadow-sm 
                ${selected === idx 
                  ? "border-orange-500 bg-orange-50 text-orange-800 shadow-md" 
                  : "border-gray-200 bg-white hover:bg-orange-50 hover:border-orange-300"}
              `}
              onClick={() => onSelect(idx)}
            >
              <div className="font-bold text-lg">Page {p.page}</div>
              <div className="text-sm text-gray-600 truncate">{p.title}</div>
              <span className={`inline-block mt-1 text-xs px-3 py-1 rounded-full font-medium 
                ${p.status === "Approved" 
                  ? "bg-green-100 text-green-700" 
                  : "bg-orange-100 text-orange-600"}
              `}>
                {p.status}
              </span>
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
}

function SummaryPanel({ page, onChangeSummary, onApprove, onRegenerate, onCustomPrompt, onViewOriginal, onApproveAll, approvedCount, totalCount, pages, selected, onSelect }: any) {
  const progressPercentage = totalCount > 0 ? (approvedCount / totalCount) * 100 : 0;

  return (
    <div className="flex-1 flex flex-col bg-white rounded-2xl shadow-xl px-4 py-4 border border-orange-100">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-brand-blue flex items-center gap-2">
          <svg className="w-7 h-7 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
          </svg>
          <span className="text-brand-blue">AI-Powered Medical Summarization</span>
        </h2>
        <div className="text-md font-medium text-brand-blue-300">Progress: <span className="text-orange-600">{approvedCount}</span> of <span className="text-brand-blue-300">{totalCount}</span> approved</div>
      </div>
      <div className="w-full bg-brand-blue-300/[20%] rounded-full h-2.5 mb-6">
        <div className="bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out" style={{ width: `${progressPercentage}%` }}></div>
      </div>

      <div className="grid grid-cols-3 gap-4">
      <div className="col-span-2">
        <div className="flex-1 flex flex-col">
          <div className="flex items-center gap-4 mb-4">
            <h3 className="font-bold text-brand-blue-300">{page.title}</h3>
            <div className="flex-1 flex justify-end gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm" onClick={onRegenerate}>
                    <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M4 4v5h.582M20 20v-5h-.581"/><path d="M5.077 19A9 9 0 1 0 6.5 6.5L4 9.5"/></svg>
                  </Button>
                </TooltipTrigger>
                <TooltipContent variant="brand">
                  <p>Regenerate</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm" onClick={onCustomPrompt}>
                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M16.862 3.487a2.6 2.6 0 1 1 3.677 3.677L7.5 20.205l-4.5 1 1-4.5L16.862 3.487Z"/><path d="M15 6.5 17.5 9"/></svg>
               </Button>
                </TooltipTrigger>
                <TooltipContent variant="brand">
                   <p>Custom Prompt</p>
                </TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-100 text-sm font-medium transition flex items-center gap-1 shadow-sm" onClick={onViewOriginal}>
                <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path d="M1.5 12S5 5 12 5s10.5 7 10.5 7-3.5 7-10.5 7S1.5 12 1.5 12Z"/><circle cx="12" cy="12" r="3"/></svg>
                </Button>
                </TooltipTrigger>
                <TooltipContent variant="brand">
                  <p>View Original</p>
                  </TooltipContent>
              </Tooltip>
            </div>
          </div>
          <div className="mb-6 flex-1 flex flex-col">
            <label className="block text-sm font-medium text-gray-700 mb-2">AI-Generated Summary</label>
            <textarea
              className="w-full border border-gray-200 rounded-lg px-4 py-3 min-h-[150px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y flex-1"
              value={page.aiSummary}
              onChange={e => onChangeSummary(e.target.value)}
            />
            <div className="flex items-center gap-2 mt-4 text-xs text-gray-500">
              <span className="bg-orange-50 text-orange-600 px-2 py-1 rounded-full font-medium">Generated by Azure OpenAI GPT-4</span>
              <span className="bg-brand-blue/[25%] text-brand-blue px-2 py-1 rounded-full font-medium">Medical AI Model</span>
            </div>
          </div>
          <div className="mb-6 flex-1 flex flex-col">
            <label className="block text-sm font-medium text-gray-700 mb-2">Original Content Preview</label>
            <div className="bg-gray-50 border border-gray-200 rounded-lg px-4 py-3 text-gray-700 text-sm min-h-[150px] overflow-y-auto flex-1 custom-scrollbar">
              {page.original}
            </div>
          </div>
          <div className="flex gap-3 mt-2 justify-start">
            <Button variant={'outline'} className="text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2" onClick={onApprove}>
              <Check className="w-5 h-5" />
              Approve
            </Button>
            <Button variant={'outline'} className="text-brand-blue border-brand-blue hover:bg-brand-blue hover:text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 flex items-center gap-2" onClick={onApproveAll}>
              <CheckCheck className="w-5 h-5" />
              Approve All ({pages.filter((p: any) => p.status === "Pending Review").length} remaining)
            </Button>
          </div>
        </div>      
        </div>
      <div className="">
        <PageList pages={pages} selected={selected} onSelect={onSelect} />
      </div>
      </div>

      {/* <div className="flex gap-8 flex-1">
        <div className="w-80 flex-shrink-0">
          <PageList pages={pages} selected={selected} onSelect={onSelect} />
        </div>
        
      </div> */}
    </div>
  );
}

export default function AiSummarizationPanel() {
  const [selected, setSelected] = useState(0);
  const [pages, setPages] = useState<any[]>([]);
  const [showPromptModal, setShowPromptModal] = useState(false);
  const [customPrompt, setCustomPrompt] = useState("");
  const [courses, setCourses] = useState<any[]>([]);
  const [selectedCourseId, setSelectedCourseId] = useState<string | null>("");
  const [loading, setLoading] = useState(false);
  const [showApproveAllModal, setShowApproveAllModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [selectedCourseBlobUrl, setSelectedCourseBlobUrl] = useState<string | null>(null);

  useEffect(() => {
    // Fetch courses on mount
    fetch(process.env.NEXT_PUBLIC_END_POINT+"/api/courses")
      .then(res => res.json())
      .then(data => setCourses(data));
  }, []);

  const handleCourseSelect = async (value:string) => {
    const courseId = value;
    setSelectedCourseId(courseId);
    setLoading(true);
    const res = await fetch(process.env.NEXT_PUBLIC_END_POINT+'/api/courses/'+courseId);
    const data = await res.json();
    // Map pdf_output to the format expected by the UI
    const mappedPages = (data.pdf_output || []).map((p: any, idx: number) => ({
      page: p.page || idx + 1,
      title: `Page ${p.page || idx + 1}`,
      status: "Pending Review",
      aiSummary: p.imagetext || "",
      original: p.imagetext || "",
      imageassestid: p.imageassestid
    }));
    setPages(mappedPages);
    setSelected(0);
    setLoading(false);
    setSelectedCourseBlobUrl(data.blob_url || null);
  };

  const handleChangeSummary = (val: string) => {
    setPages(pages => pages.map((p, idx) => idx === selected ? { ...p, aiSummary: val } : p));
  };
  const handleApprove = () => {
    setPages(pages => pages.map((p, idx) => idx === selected ? { ...p, status: "Approved" } : p));
  };
  const handleApproveAll = () => {
    setPages(pages => pages.map(p => ({ ...p, status: "Approved" })));
  };
  const handleRegenerate = async () => {
    const text = pages[selected].aiSummary;
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_END_POINT}/api/rephrase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text })
      });
      const data = await res.json();
      if (data && data.rephrased) {
        setPages(pages => pages.map((p, idx) => idx === selected ? { ...p, aiSummary: data.rephrased } : p));
      } else if (typeof data === 'string') {
        setPages(pages => pages.map((p, idx) => idx === selected ? { ...p, aiSummary: data } : p));
      }
    } catch (err) {
      alert('Failed to rephrase.');
    }
  };
  const handleCustomPrompt = () => {
    setShowPromptModal(true);
  };
  const handleViewOriginal = () => {
    if (selectedCourseBlobUrl) {
      window.open(selectedCourseBlobUrl, '_blank');
    } else {
      alert("No original content URL available.");
    }
  };
  const handlePromptCancel = () => {
    setShowPromptModal(false);
    setCustomPrompt("");
  };
  const handlePromptGenerate = async () => {
    const summaryText = pages[selected].aiSummary;
    const promptText = customPrompt;
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_END_POINT}/api/customrephrase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: summaryText, prompt: promptText })
      });
      const data = await res.json();
      if (data && data.result) {
        setPages(pages => pages.map((p, idx) => idx === selected ? { ...p, aiSummary: data.result } : p));
      } else {
        alert('Failed to get rephrased result.');
      }
    } catch (err) {
      alert('Failed to get rephrased result.');
    }
    setShowPromptModal(false);
    setCustomPrompt("");
  };
  const handleApproveAllClick = () => {
    setShowApproveAllModal(true);
  };
  const handleApproveAllConfirm = async () => {
    setShowApproveAllModal(false);
    handleApproveAll();

    // Get user email from localStorage token
    let userEmail = "unknown";
    try {
      const token = localStorage.getItem("token");
      if (token) {
        const userStr = atob(token.split(".")[1]);
        const user = JSON.parse(userStr);
        userEmail = user.email || "unknown";
      }
    } catch {}

    // Send approved data to backend
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_END_POINT}/api/courses/${selectedCourseId}/approve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          content: pages.map(p => ({ ...p, status: "Approved" })),
          approved_by: userEmail,
          comment: ''
        })
      });
      if (!res.ok) {
        alert('Failed to save approved content!');
      } else {
        setSuccessMessage('Content approved and saved!');
        setTimeout(() => setSuccessMessage(""), 3000);
      }
    } catch (err) {
      alert('Failed to save approved content!');
    }
  };
  const handleApproveAllCancel = () => {
    setShowApproveAllModal(false);
  };

  return (
    <div className="w-full px-4 py-4 flex flex-col items-stretch justify-start relative">
      {/* Course selection form */}
      {/* <div className="w-auto p-8 bg-white rounded-2xl shadow-lg p-8 mt-8 mb-8"> */}
        <form className="flex gap-4 items-end py-4" onSubmit={e => e.preventDefault()}>
          <div className="flex-1">
            <Label htmlFor="course" className="py-2">Course</Label>
            <Select
                value={selectedCourseId || ''}
                onValueChange={(value) => handleCourseSelect(value)}
              >
                <SelectTrigger className="w-auto focus:ring-2 focus:ring-orange-200">
                  <SelectValue placeholder="Select Course" />
                </SelectTrigger>
                <SelectContent>
                  {courses.map((c: any) => (
                // <option key={c.id} value={c.id}>{c.title} ({c.domain})</option>
                  <SelectItem key={c.id} value={c.id.toString()}>{c.title} ({c.domain})</SelectItem>
      ))}
                  </SelectContent>
              </Select>
            {/* <label className="block mb-1 font-medium">Select Course</label>
            <select
              className="w-full border border-gray-200 rounded px-3 py-2"
              value={selectedCourseId || ''}
              onChange={handleCourseSelect}
            >
              <option value="">-- Choose a course --</option>
              {courses.map((c: any) => (
                <option key={c.id} value={c.id}>{c.title} ({c.domain})</option>
              ))}
            </select> */}
          </div>
        </form>
      {/* </div> */}
      {loading && <div className="text-center text-lg text-orange-500">Loading course data...</div>}
      {!loading && pages.length > 0 && (
        // <div className="w-full bg-white rounded-2xl shadow-lg p-8 mt-8">
          <SummaryPanel
            page={pages[selected]}
            onChangeSummary={handleChangeSummary}
            onApprove={handleApprove}
            onRegenerate={handleRegenerate}
            onCustomPrompt={handleCustomPrompt}
            onViewOriginal={handleViewOriginal}
            onApproveAll={handleApproveAllClick}
            approvedCount={pages.filter((p: any) => p.status === "Approved").length}
            totalCount={pages.length}
            pages={pages}
            selected={selected}
            onSelect={setSelected}
          />
        // </div>
      )}

      {/* Custom Prompt Modal */}
      {showPromptModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full animate-fade-in-up relative">
            <h3 className="text-xl font-bold text-gray-800 mb-4">Custom AI Prompt</h3>
            <textarea
              className="w-full border border-gray-300 rounded-lg p-3 min-h-[120px] focus:ring-2 focus:ring-orange-200 focus:border-orange-300 transition-colors duration-200 resize-y"
              placeholder="Enter your custom prompt here to regenerate the summary..."
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
            ></textarea>
            <div className="flex justify-end gap-3 mt-6">
              <button
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
                onClick={handlePromptCancel}
              >
                Cancel
              </button>
              <button
                className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
                onClick={handlePromptGenerate}
              >
                Generate
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Approve All Confirmation Modal */}
      {showApproveAllModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-sm w-full animate-fade-in-up relative text-center">
            <h3 className="text-xl font-bold text-gray-800 mb-4">Confirm Approval</h3>
            <p className="text-gray-600 mb-6">Are you sure you want to approve all pending summaries for this course? This action cannot be undone.</p>
            <div className="flex justify-center gap-4">
              <button
                className="bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
                onClick={handleApproveAllCancel}
              >
                Cancel
              </button>
              <button
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
                onClick={handleApproveAllConfirm}
              >
                Confirm Approve All
              </button>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="fixed bottom-6 right-6 bg-green-500 text-white px-6 py-3 rounded-lg shadow-xl text-lg font-semibold animate-fade-in-right z-50">
          {successMessage}
        </div>
      )}
    </div>
  );
}